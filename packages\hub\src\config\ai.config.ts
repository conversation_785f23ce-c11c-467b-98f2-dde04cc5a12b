import { logger } from '../utils/logger';

/**
 * AI Configuration for TravelViz Parser
 * Centralizes AI model settings, prompts, and parsing configurations
 */

export interface AIModelConfig {
  id: string;
  name: string;
  provider: 'openrouter' | 'openai' | 'anthropic';
  maxTokens: number;
  costPer1kTokens: number;
  speedRating: number; // 1-10, higher is faster
  qualityRating: number; // 1-10, higher is better
}

export interface AIParsingConfig {
  maxTokens: number;
  temperature: number;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
  enableFallback: boolean;
}

export const AI_MODELS: Record<string, AIModelConfig> = {
  // Primary model - Gemini Flash 2.0 (via Google API)
  'gemini-flash-2.0': {
    id: 'gemini-2.0-flash-exp',
    name: 'Gemini Flash 2.0',
    provider: 'openrouter', // Will be handled specially in the service
    maxTokens: 8192, // Maximum output tokens for Gemini 2.0
    costPer1kTokens: 0, // Free via Google API
    speedRating: 9,
    qualityRating: 9,
  },
  
  // First fallback - Kimi-K2 (Free tier via OpenRouter) - Faster than DeepSeek
  'kimi-k2-free': {
    id: 'moonshotai/kimi-k2:free',
    name: 'Kimi-K2 (Free)',
    provider: 'openrouter',
    maxTokens: 66000, // 66K output tokens - much higher than Gemini
    costPer1kTokens: 0, // Free tier
    speedRating: 8, // Faster than DeepSeek
    qualityRating: 8,
  },

  // Second fallback - DeepSeek Chat v3 (Free tier via OpenRouter) - Highest capacity
  'deepseek-chat-free': {
    id: 'deepseek/deepseek-chat-v3-0324:free',
    name: 'DeepSeek Chat v3 (Free)',
    provider: 'openrouter',
    maxTokens: 164000, // 164K output tokens - highest capacity
    costPer1kTokens: 0, // Free tier
    speedRating: 6, // Slower than Kimi but very high capacity
    qualityRating: 8,
  },
  
  // Second fallback - Gemini Flash 2.0 (via OpenRouter)
  'gemini-flash-openrouter': {
    id: 'google/gemini-2.0-flash-001',
    name: 'Gemini Flash 2.0 (OpenRouter)',
    provider: 'openrouter',
    maxTokens: 4000,
    costPer1kTokens: 0.00005, // Very cheap on OpenRouter
    speedRating: 8,
    qualityRating: 8,
  },
  
  // Legacy models kept for compatibility
  'claude-3-haiku': {
    id: 'anthropic/claude-3-haiku',
    name: 'Claude 3 Haiku',
    provider: 'openrouter',
    maxTokens: 4000,
    costPer1kTokens: 0.00025,
    speedRating: 9,
    qualityRating: 8,
  },
};

export const AI_CONFIG = {
  provider: 'openrouter' as const,
  apiKey: process.env.OPENROUTER_API_KEY!,
  baseURL: 'https://openrouter.ai/api/v1',
  
  // Model selection - Optimized flow: Gemini Flash 2.0 -> Kimi-K2 -> DeepSeek v3 -> Gemini OpenRouter
  primaryModel: process.env.AI_MODEL || 'gemini-flash-2.0',
  fallbackModels: ['kimi-k2-free', 'deepseek-chat-free', 'gemini-flash-openrouter'],
  
  // Parsing configuration
  parsing: {
    maxTokens: 4000,
    temperature: 0.3, // Lower = more consistent parsing
    timeout: 75000, // 75 seconds - increased for complex PDF processing
    retryAttempts: 3, // Increased retry attempts
    retryDelay: 2000, // 2 seconds - increased delay
    enableFallback: true,
  } as AIParsingConfig,

  // Request headers
  headers: {
    'HTTP-Referer': 'https://travelviz.app',
    'X-Title': 'TravelViz',
  },

  // Simplified system prompt for reduced token usage (~150 tokens)
  systemPrompt: `Extract trip data from the conversation as JSON:
{
  "title": "destination + dates",
  "startDate": "YYYY-MM-DD",
  "endDate": "YYYY-MM-DD",
  "destination": "main city/country",
  "activities": [{
    "name": "activity",
    "type": "flight|hotel|activity|transport|food|other",
    "startTime": "YYYY-MM-DDTHH:mm:ss",
    "location": {"address": "location", "lat": 0, "lng": 0},
    "price": 0,
    "currency": "USD",
    "dayNumber": 1,
    "confidence": 0.8
  }],
  "metadata": {"totalDays": 0, "confidence": 0.8}
}
Types: flight=air travel, hotel=accommodation, activity=attractions, transport=ground travel, food=dining
Only extract explicitly mentioned items. Return {"error":true,"message":"reason"} if no trip found.`,

  // Progress messages for SSE updates
  progressMessages: {
    initializing: 'Starting AI analysis...',
    extracting: 'Analyzing conversation structure...',
    parsing: 'Extracting trip details...',
    enhancing: 'Adding location information...',
    validating: 'Validating dates and times...',
    finalizing: 'Creating your itinerary...',
  },
};

// Validate configuration on startup
export function validateAIConfig(): void {
  const hasGeminiKey = !!process.env.GOOGLE_GEMINI_API_KEY;
  const hasOpenRouterKey = !!process.env.OPENROUTER_API_KEY;
  
  if (!hasOpenRouterKey && !hasGeminiKey) {
    logger.error('Neither OPENROUTER_API_KEY nor GOOGLE_GEMINI_API_KEY is configured');
    throw new Error('AI service configuration error: At least one AI API key is required');
  }

  const primaryModel = AI_MODELS[AI_CONFIG.primaryModel];
  if (!primaryModel) {
    logger.warn(`Primary AI model '${AI_CONFIG.primaryModel}' not found in configuration`);
  }

  logger.info('AI configuration loaded', {
    provider: AI_CONFIG.provider,
    primaryModel: AI_CONFIG.primaryModel,
    fallbackEnabled: AI_CONFIG.parsing.enableFallback,
    fallbackModels: AI_CONFIG.fallbackModels,
    hasGeminiKey,
    hasOpenRouterKey,
  });
}

// Get model configuration
export function getModelConfig(modelKey: string): AIModelConfig | null {
  return AI_MODELS[modelKey] || null;
}

/**
 * Estimate content complexity based on text length and structure
 */
export function estimateContentComplexity(content: string): 'simple' | 'medium' | 'complex' | 'very_complex' {
  const length = content.length;
  const dayMatches = content.match(/day\s*\d+/gi) || [];
  const activityIndicators = content.match(/\d+:\d+|morning|afternoon|evening|\d+\s*(am|pm)/gi) || [];
  const locationIndicators = content.match(/hotel|restaurant|museum|tower|airport|station/gi) || [];

  const dayCount = dayMatches.length;
  const activityCount = activityIndicators.length;
  const locationCount = locationIndicators.length;

  // Simple: Short content, few days/activities
  if (length < 2000 && dayCount <= 3 && activityCount <= 10) {
    return 'simple';
  }

  // Medium: Moderate content, up to a week
  if (length < 5000 && dayCount <= 7 && activityCount <= 25) {
    return 'medium';
  }

  // Complex: Longer content, multi-week trips
  if (length < 15000 && dayCount <= 15 && activityCount <= 60) {
    return 'complex';
  }

  // Very complex: Very long content, extensive trips
  return 'very_complex';
}

/**
 * Select optimal model based on content complexity
 */
export function selectOptimalModel(content: string): string {
  const complexity = estimateContentComplexity(content);

  switch (complexity) {
    case 'simple':
    case 'medium':
      // Gemini 2.0 is perfect for simple to medium complexity
      return AI_CONFIG.primaryModel;

    case 'complex':
      // For complex content, start with Kimi-K2 for better token capacity and speed
      return 'kimi-k2-free';

    case 'very_complex':
      // For very complex content, prefer Kimi-K2 for better speed/capacity balance
      // DeepSeek v3 has higher capacity but is slower and more prone to timeouts
      return 'kimi-k2-free';

    default:
      return AI_CONFIG.primaryModel;
  }
}

/**
 * Get fallback models based on complexity
 */
export function getFallbackModels(complexity: 'simple' | 'medium' | 'complex' | 'very_complex'): string[] {
  switch (complexity) {
    case 'simple':
    case 'medium':
      // For simple/medium: Gemini -> Kimi -> DeepSeek
      return ['kimi-k2-free', 'deepseek-chat-free', 'gemini-flash-openrouter'];

    case 'complex':
      // For complex: Kimi -> DeepSeek -> Gemini (fallback to lower capacity)
      return ['deepseek-chat-free', 'gemini-flash-2.0', 'gemini-flash-openrouter'];

    case 'very_complex':
      // For very complex: DeepSeek -> Gemini (avoid repeating primary model)
      return ['deepseek-chat-free', 'gemini-flash-openrouter'];

    default:
      return AI_CONFIG.fallbackModels;
  }
}

// Select best available model based on requirements (legacy function)
export function selectModelByRequirements(requirements: {
  preferSpeed?: boolean;
  preferQuality?: boolean;
  maxCost?: number;
}): AIModelConfig {
  const { preferSpeed = false, preferQuality = false, maxCost = Infinity } = requirements;

  // Filter models by cost constraint
  const affordableModels = Object.values(AI_MODELS).filter(
    model => model.costPer1kTokens <= maxCost
  );

  if (affordableModels.length === 0) {
    // Return cheapest model if none are affordable
    return Object.values(AI_MODELS).sort((a, b) => a.costPer1kTokens - b.costPer1kTokens)[0];
  }

  // Sort by preference
  if (preferSpeed) {
    return affordableModels.sort((a, b) => b.speedRating - a.speedRating)[0];
  } else if (preferQuality) {
    return affordableModels.sort((a, b) => b.qualityRating - a.qualityRating)[0];
  }

  // Default: balance speed and quality
  return affordableModels.sort((a, b) => {
    const scoreA = (a.speedRating + a.qualityRating) / 2;
    const scoreB = (b.speedRating + b.qualityRating) / 2;
    return scoreB - scoreA;
  })[0];
}