#!/usr/bin/env node

/**
 * Test script to verify PDF import timeout fixes
 */

const axios = require('axios');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env.local') });

const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3001';

// Test configuration
const TEST_CONFIG = {
  timeout: 90000, // 90 seconds to match our new timeout
  pollingInterval: 2000, // 2 seconds
  maxPollingTime: 180000, // 3 minutes
};

// Sample complex content to test timeout handling
const COMPLEX_CONTENT = `
European Adventure - 15 Days
March 15-30, 2024

Day 1-4: London, UK
March 15: Arrive London Heathrow 10:30 AM, Heathrow Express to city (£25), Check into The Z Hotel Piccadilly (£120/night)
March 16: British Museum (free), Borough Market lunch (£15), Fortnum & Mason afternoon tea (£65)
March 17: Tower of London (£29.90), Tower Bridge walk, West End show Hamilton (£85)
March 18: Oxford day trip by train (£30 return), walking tour, pub lunch

Day 5-8: Madrid, Spain  
March 19: Flight London to Madrid (£80 RyanAir), arrive 2 PM, Check into Hotel Urban (€150/night)
March 20: Prado Museum (€15), Retiro Park, La Latina tapas tour (€75)
March 21: Royal Palace (€12), Almudena Cathedral, Malasaña neighborhood
March 22: Toledo day trip (€25 train), cathedral and alcázar visit

Day 9-12: Lisbon, Portugal
March 23: Flight Madrid to Lisbon (€60), Check into Memmo Alfama Hotel (€130/night)
March 24: Jerónimos Monastery (€10), Belém Tower (€6), pastéis de nata tasting
March 25: Sintra day trip (€15 train), Pena Palace (€14), Quinta da Regaleira (€11)
March 26: Fado dinner in Alfama (€45), Port wine tasting (€25)

Day 13-15: Porto, Portugal
March 27: Train Lisbon to Porto (€25), Check into The Yeatman (€200/night)
March 28: Port wine cellars tour (€20), Livraria Lello bookstore (€5), Clérigos Tower (€6)
March 29: Douro Valley day trip (€85), river cruise and quintas visit
March 30: Return flight Porto to NYC (€180)

Total Budget: €5,000 per person
Includes: Accommodation, transportation, meals, activities, flights
`.repeat(10); // Make it very long to test timeout handling

async function testPDFImportTimeout() {
  console.log('🧪 Testing PDF Import Timeout Fixes');
  console.log('=====================================');
  
  try {
    // Test 1: Create parse session
    console.log('\n1. Creating parse session...');
    const parseResponse = await axios.post(
      `${API_BASE_URL}/api/v1/import/parse-simple`,
      {
        content: COMPLEX_CONTENT,
        source: 'chatgpt'
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.TEST_AUTH_TOKEN || 'test-token'}`
        },
        timeout: 10000
      }
    );
    
    const sessionId = parseResponse.data.data.importId;
    console.log(`✅ Session created: ${sessionId}`);
    
    // Test 2: Poll for completion with timeout handling
    console.log('\n2. Polling for completion...');
    const startTime = Date.now();
    let attempts = 0;
    let lastStatus = 'pending';
    
    while (Date.now() - startTime < TEST_CONFIG.maxPollingTime) {
      attempts++;
      
      try {
        const statusResponse = await axios.get(
          `${API_BASE_URL}/api/v1/import/parse-simple/${sessionId}`,
          {
            headers: {
              'Authorization': `Bearer ${process.env.TEST_AUTH_TOKEN || 'test-token'}`
            },
            timeout: 5000
          }
        );
        
        const { status, progress, currentStep, error } = statusResponse.data.data;
        
        if (status !== lastStatus) {
          console.log(`📊 Status: ${status} (${progress}%) - ${currentStep}`);
          lastStatus = status;
        }
        
        if (status === 'complete') {
          console.log('✅ Import completed successfully!');
          console.log(`⏱️  Total time: ${Math.round((Date.now() - startTime) / 1000)}s`);
          console.log(`🔄 Total attempts: ${attempts}`);
          return;
        }
        
        if (status === 'error') {
          console.log(`❌ Import failed: ${error}`);
          console.log(`⏱️  Failed after: ${Math.round((Date.now() - startTime) / 1000)}s`);
          return;
        }
        
      } catch (pollError) {
        console.log(`⚠️  Polling error (attempt ${attempts}):`, pollError.message);
      }
      
      // Wait before next poll
      await new Promise(resolve => setTimeout(resolve, TEST_CONFIG.pollingInterval));
    }
    
    console.log('⏰ Polling timed out after 3 minutes');
    console.log(`🔄 Total attempts: ${attempts}`);
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.code === 'ECONNABORTED') {
      console.log('🔍 This indicates a timeout - check if the server is running');
    } else if (error.response) {
      console.log('🔍 Server responded with:', error.response.status, error.response.data);
    }
  }
}

// Run the test
if (require.main === module) {
  testPDFImportTimeout().catch(console.error);
}

module.exports = { testPDFImportTimeout };
